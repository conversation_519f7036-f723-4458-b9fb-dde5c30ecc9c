import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [
    react({
      // Enable React Fast Refresh
      fastRefresh: true,
      // Optimize JSX runtime
      jsxRuntime: 'automatic'
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  base: './',
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    minify: 'terser',
    sourcemap: false, // Disable sourcemaps for production
    cssCodeSplit: true, // Enable CSS code splitting
    assetsInlineLimit: 4096, // Inline assets smaller than 4kb
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn'],
        passes: 2, // Reduced to 2 passes for compatibility
        unsafe: true,
        unsafe_comps: true,
        unsafe_math: true,
        unsafe_methods: true,
        dead_code: true,
        reduce_vars: true,
        collapse_vars: true,
        sequences: true,
        properties: true,
        conditionals: true,
        comparisons: true,
        evaluate: true,
        booleans: true,
        loops: true,
        unused: true,
        hoist_funs: true,
        hoist_vars: true,
        if_return: true,
        inline: true,
        side_effects: true,
        pure_getters: true,
        negate_iife: true,
      },
      mangle: {
        safari10: true,
        toplevel: true,
      },
      format: {
        comments: false,
        ascii_only: true,
      }
    },
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Dynamic chunking strategy for better optimization
          if (id.includes('node_modules')) {
            // Vendor chunks
            if (id.includes('react') || id.includes('react-dom')) {
              return 'vendor'
            }
            if (id.includes('react-router')) {
              return 'router'
            }
            if (id.includes('react-markdown') || id.includes('remark')) {
              return 'markdown'
            }
            if (id.includes('@fortawesome')) {
              return 'fontawesome'
            }
            if (id.includes('zustand') || id.includes('uuid')) {
              return 'utils'
            }
            if (id.includes('jszip') || id.includes('mammoth') || id.includes('pdf-parse') ||
                id.includes('xlsx') || id.includes('sharp') || id.includes('tesseract')) {
              return 'fileProcessing'
            }
            // Other vendor dependencies
            return 'vendor-misc'
          }

          // Application chunks
          if (id.includes('/pages/')) {
            return 'pages'
          }
          if (id.includes('/components/artifacts/')) {
            return 'artifacts'
          }
          if (id.includes('/components/')) {
            return 'components'
          }
          if (id.includes('/utils/') || id.includes('/store/')) {
            return 'app-utils'
          }
        },
        chunkFileNames: (chunkInfo) => {
          return `assets/js/[name]-[hash].js`
        },
        entryFileNames: `assets/js/[name]-[hash].js`,
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || []
          const ext = info[info.length - 1]
          if (/\.(css)$/.test(assetInfo.name || '')) {
            return `assets/css/[name]-[hash].${ext}`
          }
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name || '')) {
            return `assets/images/[name]-[hash].${ext}`
          }
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name || '')) {
            return `assets/fonts/[name]-[hash].${ext}`
          }
          return `assets/[name]-[hash].${ext}`
        },
        // Optimize chunk loading
        inlineDynamicImports: false,
        preserveModules: false,
      },
      // External dependencies for Electron (handled by main process)
      external: (id) => {
        return ['electron', 'fs', 'path', 'os', 'crypto', 'child_process'].some(ext => id.startsWith(ext))
      },
      // Tree-shaking optimizations
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false,
        unknownGlobalSideEffects: false
      }
    },
    chunkSizeWarningLimit: 500,
    reportCompressedSize: true,
    // Additional build optimizations
    target: 'esnext',
    modulePreload: {
      polyfill: false
    }
  },
  server: {
    port: 5173,
    host: true,
    strictPort: true,
    proxy: {
      // Proxy for Ollama
      '/api/ollama': {
        target: 'http://localhost:11434',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/ollama/, ''),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Ollama proxy error:', err);
          });
        },
      },
      // Proxy for LM Studio
      '/api/lmstudio': {
        target: 'http://127.0.0.1:1234',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/lmstudio/, ''),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('LM Studio proxy error:', err);
          });
        },
      },
    },
  },
  preview: {
    port: 4173,
    host: true,
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-dom/client',
      'react/jsx-runtime',
      'react-router-dom',
      'zustand',
      'uuid',
      '@fortawesome/fontawesome-svg-core',
      '@fortawesome/free-solid-svg-icons',
      '@fortawesome/react-fontawesome',
      'react-markdown',
      'remark-gfm'
    ],
    exclude: [
      'sharp',
      'tesseract.js',
      'pdf-parse',
      'mammoth',
      'xlsx',
      'better-sqlite3',
      'electron',
      'chokidar',
      'mime-types'
    ],
    // Force optimization of problematic dependencies
    force: true
  },
  // Additional performance optimizations
  esbuild: {
    // Drop console and debugger in production
    drop: ['console', 'debugger'],
    // Optimize for modern browsers
    target: 'esnext',
    // Enable tree-shaking
    treeShaking: true,
    // Minify identifiers
    minifyIdentifiers: true,
    // Minify syntax
    minifySyntax: true,
    // Minify whitespace
    minifyWhitespace: true,
    // Legal comments
    legalComments: 'none'
  },
  // CSS optimization
  css: {
    devSourcemap: false,
    preprocessorOptions: {
      css: {
        charset: false
      }
    },
    postcss: {
      plugins: [
        // Additional PostCSS optimizations can be added here
      ]
    }
  },
  // JSON optimization
  json: {
    namedExports: true,
    stringify: false
  },
  // Worker optimization
  worker: {
    format: 'es',
    plugins: () => []
  },
  // Define global constants for better tree-shaking
  define: {
    __DEV__: JSON.stringify(false),
    __PROD__: JSON.stringify(true),
    'process.env.NODE_ENV': JSON.stringify('production')
  }
})
