import React, { useState } from 'react'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#22c55e', // Green background
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      textAlign: 'center',
      padding: '20px'
    }}>
      <h1 style={{ 
        fontSize: '3rem', 
        marginBottom: '2rem',
        textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
      }}>
        🚀 Vite + Electron Test
      </h1>
      
      <div style={{
        backgroundColor: 'rgba(255,255,255,0.1)',
        padding: '2rem',
        borderRadius: '12px',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)'
      }}>
        <h2 style={{ marginBottom: '1rem' }}>Hello World!</h2>
        <p style={{ marginBottom: '2rem', fontSize: '1.2rem' }}>
          This is an isolated Electron + Vite test application
        </p>
        
        <div style={{ marginBottom: '2rem' }}>
          <button 
            onClick={() => setCount(count + 1)}
            style={{
              backgroundColor: '#16a34a',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              fontSize: '1.1rem',
              borderRadius: '8px',
              cursor: 'pointer',
              boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
              transition: 'all 0.2s ease'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.backgroundColor = '#15803d'
              e.currentTarget.style.transform = 'translateY(-2px)'
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.backgroundColor = '#16a34a'
              e.currentTarget.style.transform = 'translateY(0)'
            }}
          >
            Count: {count}
          </button>
        </div>

        <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>
          <p>✅ React is working</p>
          <p>✅ Vite hot reload is active</p>
          <p>✅ TypeScript is configured</p>
          <p>{typeof window !== 'undefined' && (window as any).electronAPI ? '✅ Electron API detected' : '⚠️ Running in browser mode'}</p>
        </div>
      </div>

      <div style={{ 
        marginTop: '2rem', 
        fontSize: '0.8rem', 
        opacity: 0.7 
      }}>
        <p>Port: 5174 | Environment: {import.meta.env.MODE}</p>
      </div>
    </div>
  )
}

export default App
