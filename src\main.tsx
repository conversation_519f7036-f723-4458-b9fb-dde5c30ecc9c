import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import App from './App'
import ErrorBoundary from './components/ErrorBoundary'
import './index.css'
import { initializeApp } from './store'

// Check if Electron API is available (only in development)
if (import.meta.env.DEV) {
  console.log('Window electronAPI available:', !!window.electronAPI)
  if (window.electronAPI) {
    console.log('ElectronAPI methods:', Object.keys(window.electronAPI))
  } else {
    console.warn('ElectronAPI not available - running in browser mode')
  }
}

// Initialize the app
initializeApp().catch((error) => {
  console.error('App initialization failed:', error)
  // Continue rendering the app even if initialization fails
})

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ErrorBoundary>
      <App />
    </ErrorBoundary>
  </React.StrictMode>,
)
