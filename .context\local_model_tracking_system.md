# Local Model Tracking System Documentation

## Overview

The ChatLo application has a comprehensive local model tracking system that monitors and manages connections to local AI model providers (Ollama and LM Studio). This document outlines all automatic tracking mechanisms, their triggers, and current status.

## Architecture

### Core Components

1. **LocalModelService** (`src/services/localModelService.ts`)
   - Main service for checking local model availability
   - Handles connections to Ollama and LM Studio
   - Provides model discovery and status checking

2. **NetworkStore** (`src/stores/networkStore.ts`)
   - Zustand store managing network and local model state
   - Handles private mode toggling
   - Manages local model availability flags

3. **NetworkStatus Component** (`src/components/NetworkStatus.tsx`)
   - UI component displaying connection status
   - Previously handled automatic periodic checking

## Automatic Tracking Mechanisms

### 1. Service Detection Methods

#### Ollama Detection
```typescript
// Location: src/services/localModelService.ts:67-121
async checkOllama(): Promise<{ connected: boolean; models: LocalModel[]; error?: string }>
```
- **Endpoint**: `http://localhost:11434/api/tags`
- **Timeout**: 2 seconds (reduced from 5 seconds)
- **Browser Mode**: Automatically skipped to prevent connection errors
- **Error Handling**: Silent failures, no console spam

#### LM Studio Detection
```typescript
// Location: src/services/localModelService.ts:123-178
async checkLMStudio(): Promise<{ connected: boolean; models: LocalModel[]; error?: string }>
```
- **Endpoint**: `http://127.0.0.1:1234/v1/models`
- **Timeout**: 2 seconds (reduced from 5 seconds)
- **Browser Mode**: Automatically skipped to prevent connection errors
- **Error Handling**: Silent failures, no console spam

### 2. Automatic Triggers (DISABLED)

#### ❌ Periodic Checking (DISABLED)
```typescript
// Previously in: src/components/NetworkStatus.tsx:25-35
// DISABLED: useEffect with 30-second interval
```
- **Status**: DISABLED to prevent connection spam
- **Previous Behavior**: Checked every 30 seconds when private mode enabled
- **Reason for Disabling**: Caused continuous connection errors when services unavailable

#### ❌ Private Mode Toggle (DISABLED)
```typescript
// Previously in: src/stores/networkStore.ts:51-53
// DISABLED: Automatic checkLocalModels() on private mode enable
```
- **Status**: DISABLED to prevent automatic connection attempts
- **Previous Behavior**: Automatically checked local models when enabling private mode
- **Reason for Disabling**: Blocked app functionality when services unavailable

#### ❌ Setup Wizard Auto-Check (DISABLED)
```typescript
// Previously in: src/components/LocalModelSetupWizard.tsx:33-38
// DISABLED: useEffect checking models when wizard opens
```
- **Status**: DISABLED to prevent connection errors
- **Previous Behavior**: Checked local models when setup wizard opened
- **Reason for Disabling**: Caused errors during wizard initialization

### 3. Manual Triggers (ACTIVE)

#### ✅ Settings Page Test Button
```typescript
// Location: src/components/Settings.tsx:81-104
const handleTestLocalModels = async () => {
  // Manual testing of local model connections
}
```
- **Status**: ACTIVE - User-initiated testing
- **Trigger**: "Test Connections" button in Settings
- **Purpose**: Allow users to manually verify local model availability

#### ✅ Chat Settings Model Loading
```typescript
// Location: src/components/ChatSettingsDrawer.tsx:47-55
// Conditional checking when private mode enabled and no models found
```
- **Status**: ACTIVE - Contextual checking
- **Trigger**: Opening chat settings in private mode with no models
- **Purpose**: Guide users to set up local models when needed

## Configuration System

### Default Settings
```typescript
// Location: src/store/index.ts:255-270
localModelServers: {
  ollama: { enabled: false, baseUrl: 'localhost', port: 11434 },
  lmstudio: { enabled: false, baseUrl: '127.0.0.1', port: 1234 }
}
```

### Service Enablement Check
```typescript
// Location: src/services/localModelService.ts:61-64
private isServiceEnabled(service: 'ollama' | 'lmstudio'): boolean {
  const serverConfig = this.settings?.localModelServers?.[service]
  return serverConfig?.enabled !== false // Default to enabled if not specified
}
```

## Browser vs Electron Mode Handling

### Browser Mode Detection
```typescript
const isElectron = !!window.electronAPI;
if (!isElectron) {
  console.log('Skipping [Service] check in browser mode')
  return { connected: false, models: [], error: 'Browser mode - local models not available' }
}
```

### Mode-Specific Behavior
- **Browser Mode**: All local model checks skipped automatically
- **Electron Mode**: Local model checks proceed if service enabled
- **Development**: Primarily runs in browser mode with limited functionality

## Error Handling Strategy

### Connection Failures
- **Timeout**: 2-second timeout for quick failure
- **Silent Failures**: No console logging of expected connection errors
- **Graceful Degradation**: App continues functioning without local models

### Error Messages
- **Service Disabled**: "Ollama/LM Studio is disabled in settings"
- **Browser Mode**: "Browser mode - local models not available"
- **Connection Failed**: "Connection failed: [error message]"

## State Management

### NetworkStore State
```typescript
interface NetworkState {
  isOnline: boolean
  isPrivateMode: boolean
  localModelsAvailable: boolean
  ollamaConnected: boolean
  lmStudioConnected: boolean
  localModels: LocalModel[]
}
```

### State Updates
- **Manual Only**: State updates only occur through manual user actions
- **No Auto-Refresh**: Removed automatic state refreshing to prevent errors
- **User-Driven**: All local model discovery is user-initiated

## Future Considerations

### Potential Improvements
1. **Smart Detection**: Only check when services are likely available
2. **Background Checking**: Optional background checking with user consent
3. **Service Discovery**: Automatic port scanning for local services
4. **Health Monitoring**: Periodic health checks for connected services

### Current Limitations
1. **Manual Setup Required**: Users must manually enable and test local services
2. **No Auto-Discovery**: No automatic detection of running services
3. **Static Configuration**: Service endpoints are statically configured

## Troubleshooting

### Common Issues
1. **Connection Refused Errors**: Ensure services are disabled by default
2. **Browser Mode Limitations**: Local models not available in development
3. **Timeout Issues**: 2-second timeout may be too short for slow systems

### Debug Information
- Check browser console for "Skipping [Service] check in browser mode" messages
- Verify service enabled status in settings
- Test manual connections via Settings page

## Related Files

- `src/services/localModelService.ts` - Core service implementation
- `src/stores/networkStore.ts` - State management
- `src/components/NetworkStatus.tsx` - UI status display
- `src/components/Settings.tsx` - Manual testing interface
- `src/components/LocalModelSetupWizard.tsx` - Setup guidance
- `src/components/ChatSettingsDrawer.tsx` - Model selection interface

---

**Last Updated**: 2025-01-10
**Status**: Automatic tracking disabled, manual-only operation
**Reason**: Prevent connection errors from blocking app functionality
