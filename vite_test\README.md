# Vite + Electron Test App

This is an isolated test application to verify Electron + Vite setup independently from the main ChatLo application.

## Features

- ✅ **Green Background**: Distinctive green theme for easy identification
- ✅ **React + TypeScript**: Modern React setup with TypeScript
- ✅ **Vite Hot Reload**: Fast development with Vite
- ✅ **Electron Integration**: Desktop app capabilities
- ✅ **Port 5174**: Runs on different port to avoid conflicts
- ✅ **Interactive Counter**: Simple functionality test
- ✅ **Environment Detection**: Shows if running in browser or Electron

## Quick Start

1. **Install Dependencies**
   ```bash
   cd vite_test
   npm install
   ```

2. **Run Development Server**
   ```bash
   npm run dev
   ```

3. **Browser Only (for testing)**
   ```bash
   npm run dev:vite
   # Then open http://localhost:5174
   ```

## What You Should See

- **Green background** with white text
- **"Vite + Electron Test"** title
- **Interactive counter button**
- **Status indicators** showing what's working
- **Environment info** (browser vs Electron mode)

## Troubleshooting

### If the app doesn't start:
1. Check if port 5174 is available
2. Ensure all dependencies are installed
3. Try browser-only mode first: `npm run dev:vite`

### If Electron doesn't open:
1. Check the terminal for error messages
2. Try killing any existing Node processes
3. Restart the development server

## File Structure

```
vite_test/
├── src/
│   ├── App.tsx          # Main React component (green background)
│   └── main.tsx         # React entry point
├── electron/
│   ├── main.ts          # Electron main process
│   ├── preload.ts       # Electron preload script
│   └── tsconfig.json    # TypeScript config for Electron
├── package.json         # Dependencies and scripts
├── vite.config.ts       # Vite configuration
└── tsconfig.json        # TypeScript config for React
```

## Success Indicators

When everything is working correctly, you should see:
- ✅ React is working
- ✅ Vite hot reload is active  
- ✅ TypeScript is configured
- ✅ Electron API detected (in Electron mode)

## Next Steps

If this test app works correctly, you can be confident that:
1. Your Electron + Vite setup is working
2. Port conflicts are not the issue
3. The development environment is properly configured
4. You can focus on debugging the main ChatLo application
