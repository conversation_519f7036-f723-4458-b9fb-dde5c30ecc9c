export interface LocalModel {
  id: string
  name: string
  provider: 'ollama' | 'lmstudio'
  size?: string
  modified?: string
  digest?: string
  details?: {
    format?: string
    family?: string
    families?: string[]
    parameter_size?: string
    quantization_level?: string
  }
}

export interface LocalModelProvider {
  name: string
  baseUrl: string
  isConnected: boolean
  models: LocalModel[]
}

class LocalModelService {
  private settings: any = null

  // Set settings from the app store
  setSettings(settings: any) {
    this.settings = settings
  }

  private getBaseUrl(service: 'ollama' | 'lmstudio'): string {
    // Check if we're in development mode (browser) or production (Electron)
    const isElectron = !!window.electronAPI;

    if (isElectron) {
      // In Electron, we can use direct URLs since there are no CORS restrictions
      // Default configurations
      const defaults = {
        ollama: { baseUrl: 'localhost', port: 11434 },
        lmstudio: { baseUrl: '127.0.0.1', port: 1234 } // Use 127.0.0.1 for LM Studio
      }

      // Get configuration from settings
      const serverConfig = this.settings?.localModelServers?.[service]
      const config = serverConfig || defaults[service]

      // Use direct URLs in Electron
      const url = `http://${config.baseUrl}:${config.port}`
      console.log(`Using direct URL for ${service} (Electron):`, url)
      return url
    } else {
      // In browser development mode, use the Vite proxy to avoid CORS issues
      const proxyUrl = service === 'ollama' ? '/api/ollama' : '/api/lmstudio';
      console.log(`Using proxy URL for ${service} (Browser):`, proxyUrl)
      return proxyUrl;
    }
  }

  // Check if a service is enabled in settings
  private isServiceEnabled(service: 'ollama' | 'lmstudio'): boolean {
    const serverConfig = this.settings?.localModelServers?.[service]
    return serverConfig?.enabled !== false // Default to enabled if not specified
  }

  // Check if Ollama is available and get models
  async checkOllama(): Promise<{ connected: boolean; models: LocalModel[]; error?: string }> {
    // Check if service is enabled
    if (!this.isServiceEnabled('ollama')) {
      return { connected: false, models: [], error: 'Ollama is disabled in settings' }
    }

    // In browser mode, skip the check to avoid connection errors
    const isElectron = !!window.electronAPI;
    if (!isElectron) {
      console.log('Skipping Ollama check in browser mode')
      return { connected: false, models: [], error: 'Browser mode - local models not available' }
    }

    try {
      const baseUrl = this.getBaseUrl('ollama')
      const response = await fetch(`${baseUrl}/api/tags`, {
        method: 'GET',
        signal: AbortSignal.timeout(2000), // Reduced timeout to 2 seconds
        mode: 'cors',
        headers: {
          'Accept': 'application/json'
        }
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        return {
          connected: false,
          models: [],
          error: `HTTP ${response.status}: ${errorText}`
        }
      }

      const data = await response.json()
      const models: LocalModel[] = data.models?.map((model: any) => ({
        id: `ollama:${model.name}`,
        name: model.name,
        provider: 'ollama' as const,
        size: model.size ? this.formatBytes(model.size) : undefined,
        modified: model.modified_at,
        digest: model.digest,
        details: model.details
      })) || []

      return { connected: true, models }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      // Don't log connection errors as they're expected when services aren't running
      return {
        connected: false,
        models: [],
        error: `Connection failed: ${errorMessage}`
      }
    }
  }

  // Check if LM Studio is available and get models
  async checkLMStudio(): Promise<{ connected: boolean; models: LocalModel[]; error?: string }> {
    // Check if service is enabled
    if (!this.isServiceEnabled('lmstudio')) {
      return { connected: false, models: [], error: 'LM Studio is disabled in settings' }
    }

    // In browser mode, skip the check to avoid connection errors
    const isElectron = !!window.electronAPI;
    if (!isElectron) {
      console.log('Skipping LM Studio check in browser mode')
      return { connected: false, models: [], error: 'Browser mode - local models not available' }
    }

    try {
      const baseUrl = this.getBaseUrl('lmstudio')
      const response = await fetch(`${baseUrl}/v1/models`, {
        method: 'GET',
        signal: AbortSignal.timeout(2000), // Reduced timeout to 2 seconds
        mode: 'cors',
        headers: {
          'Accept': 'application/json'
        }
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        return {
          connected: false,
          models: [],
          error: `HTTP ${response.status}: ${errorText}`
        }
      }

      const data = await response.json()
      const models: LocalModel[] = data.data?.map((model: any) => ({
        id: `lmstudio:${model.id}`,
        name: model.id,
        provider: 'lmstudio' as const,
        size: undefined, // LM Studio doesn't provide size info
        modified: undefined,
        digest: undefined,
        details: undefined
      })) || []

      return { connected: true, models }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      // Don't log connection errors as they're expected when services aren't running
      return {
        connected: false,
        models: [],
        error: `Connection failed: ${errorMessage}`
      }
    }
  }

  // Get all available local models
  async getAllLocalModels(): Promise<LocalModel[]> {
    const [ollamaResult, lmStudioResult] = await Promise.all([
      this.checkOllama(),
      this.checkLMStudio()
    ])

    return [...ollamaResult.models, ...lmStudioResult.models]
  }

  // Get provider status
  async getProviderStatus(): Promise<{
    ollama: LocalModelProvider
    lmstudio: LocalModelProvider
  }> {
    const [ollamaResult, lmStudioResult] = await Promise.all([
      this.checkOllama(),
      this.checkLMStudio()
    ])

    return {
      ollama: {
        name: 'Ollama',
        baseUrl: this.ollamaBaseUrl,
        isConnected: ollamaResult.connected,
        models: ollamaResult.models
      },
      lmstudio: {
        name: 'LM Studio',
        baseUrl: this.lmStudioBaseUrl,
        isConnected: lmStudioResult.connected,
        models: lmStudioResult.models
      }
    }
  }

  // Send message to local model
  async sendMessage(
    modelId: string,
    messages: Array<{ role: string; content: string }>,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    const [provider, modelName] = modelId.split(':')
    
    if (provider === 'ollama') {
      return this.sendOllamaMessage(modelName, messages, onChunk)
    } else if (provider === 'lmstudio') {
      return this.sendLMStudioMessage(modelName, messages, onChunk)
    } else {
      throw new Error(`Unknown local model provider: ${provider}`)
    }
  }

  // Send message to Ollama
  private async sendOllamaMessage(
    model: string,
    messages: Array<{ role: string; content: string }>,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    const baseUrl = this.getBaseUrl('ollama')
    const response = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        messages,
        stream: !!onChunk
      })
    })

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.statusText}`)
    }

    if (onChunk && response.body) {
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let fullResponse = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n').filter(line => line.trim())

          for (const line of lines) {
            try {
              const data = JSON.parse(line)
              if (data.message?.content) {
                fullResponse += data.message.content
                onChunk(data.message.content)
              }
            } catch (e) {
              // Ignore malformed JSON
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      return fullResponse
    } else {
      const data = await response.json()
      return data.message?.content || ''
    }
  }

  // Send message to LM Studio
  private async sendLMStudioMessage(
    model: string,
    messages: Array<{ role: string; content: string }>,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    const baseUrl = this.getBaseUrl('lmstudio')
    const response = await fetch(`${baseUrl}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        messages,
        stream: !!onChunk
      })
    })

    if (!response.ok) {
      throw new Error(`LM Studio API error: ${response.statusText}`)
    }

    if (onChunk && response.body) {
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let fullResponse = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n').filter(line => line.trim())

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6)
              if (data === '[DONE]') continue

              try {
                const parsed = JSON.parse(data)
                const content = parsed.choices?.[0]?.delta?.content
                if (content) {
                  fullResponse += content
                  onChunk(content)
                }
              } catch (e) {
                // Ignore malformed JSON
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      return fullResponse
    } else {
      const data = await response.json()
      return data.choices?.[0]?.message?.content || ''
    }
  }

  // Utility function to format bytes
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

export const localModelService = new LocalModelService()
