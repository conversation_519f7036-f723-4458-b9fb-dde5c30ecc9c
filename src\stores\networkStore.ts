import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { localModelService, LocalModel } from '../services/localModelService'

interface NetworkState {
  isOnline: boolean
  isPrivateMode: boolean
  localModelsAvailable: boolean
  ollamaConnected: boolean
  lmStudioConnected: boolean
  localModels: LocalModel[]

  // Actions
  toggleOnline: () => void
  togglePrivateMode: () => void
  setLocalModelsAvailable: (available: boolean) => void
  setOllamaConnected: (connected: boolean) => void
  setLmStudioConnected: (connected: boolean) => void
  setLocalModels: (models: LocalModel[]) => void
  checkLocalModels: () => Promise<void>
  getAvailableModels: () => LocalModel[]
}

export const useNetworkStore = create<NetworkState>()(
  persist(
    (set, get) => ({
      // Initial state
      isOnline: true,
      isPrivateMode: false,
      localModelsAvailable: false,
      ollamaConnected: false,
      lmStudioConnected: false,
      localModels: [],

      // Toggle network connectivity
      toggleOnline: () => {
        const currentState = get()
        const newOnlineState = !currentState.isOnline

        set({ isOnline: newOnlineState })
      },

      // Toggle private mode
      togglePrivateMode: () => {
        const currentState = get()
        const newPrivateMode = !currentState.isPrivateMode

        set({ isPrivateMode: newPrivateMode })

        // Don't automatically check local models to avoid connection errors
        // Users can manually check via settings if needed
        if (!newPrivateMode) {
          // When disabling private mode, ensure we're back online
          set({ isOnline: true })
        }
      },

      // Set local models availability
      setLocalModelsAvailable: (available: boolean) => {
        set({ localModelsAvailable: available })
      },

      // Set local models list
      setLocalModels: (models: LocalModel[]) => {
        set({ localModels: models })
      },

      // Set Ollama connection status
      setOllamaConnected: (connected: boolean) => {
        set({ ollamaConnected: connected })
        // Update overall local models availability
        const { lmStudioConnected } = get()
        set({ localModelsAvailable: connected || lmStudioConnected })
      },

      // Set LM Studio connection status
      setLmStudioConnected: (connected: boolean) => {
        set({ lmStudioConnected: connected })
        // Update overall local models availability
        const { ollamaConnected } = get()
        set({ localModelsAvailable: connected || ollamaConnected })
      },

      // Check for local model providers
      checkLocalModels: async () => {
        const providerStatus = await localModelService.getProviderStatus()
        const allModels = await localModelService.getAllLocalModels()

        get().setOllamaConnected(providerStatus.ollama.isConnected)
        get().setLmStudioConnected(providerStatus.lmstudio.isConnected)
        get().setLocalModels(allModels)
        get().setLocalModelsAvailable(allModels.length > 0)
      },

      // Get available models based on current mode
      getAvailableModels: () => {
        const { isPrivateMode, localModels } = get()
        if (isPrivateMode) {
          return localModels
        }
        // In non-private mode, return all models (local + external)
        // External models would be added here from other sources
        return localModels
      }
    }),
    {
      name: 'network-storage',
      partialize: (state) => ({
        isOnline: state.isOnline,
        isPrivateMode: state.isPrivateMode
      })
    }
  )
)
