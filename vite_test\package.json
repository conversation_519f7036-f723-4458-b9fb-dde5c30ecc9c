{"name": "vite-electron-test", "version": "1.0.0", "description": "Isolated Electron + Vite test app", "main": "dist/main.js", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"wait-on http://localhost:5174 && npm run dev:electron\"", "dev:vite": "vite --port 5174", "dev:electron": "cross-env NODE_ENV=development electron .", "build": "vite build && tsc -p electron", "build:electron": "tsc -p electron"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-react": "^4.0.0", "concurrently": "^8.0.0", "cross-env": "^7.0.3", "electron": "^25.0.0", "typescript": "^5.0.0", "vite": "^4.0.0", "wait-on": "^7.0.0"}, "dependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}